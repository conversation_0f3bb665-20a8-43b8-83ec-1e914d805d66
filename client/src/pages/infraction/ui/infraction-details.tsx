import { Card, Descriptions, Avatar, Typography, Spin, Empty, Tag } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft } from 'lucide-react'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { useGetInfractionById } from '@/config/queries/infraction/get-all.queries'

const { Title, Text } = Typography

const InfractionDetails = () => {
	const { id } = useParams<{ id: string }>()
	const navigate = useNavigate()

	const { data: infractionData, isLoading, error } = useGetInfractionById(id!)

	if (isLoading) {
		return (
			<div className="flex justify-center items-center py-20">
				<Spin size="large" tip="Ma'lumotlar yuklanmoqda..." />
			</div>
		)
	}

	if (error || !infractionData?.data) {
		return (
			<Card>
				<Empty description="Qoidabuzarlik ma'lumotlari topilmadi" />
			</Card>
		)
	}

	const infraction = infractionData.data

	return (
		<div className="p-6">
			<Card>
				<div className="mb-4 flex items-center gap-3">
					<ArrowLeft
						className="cursor-pointer hover:text-blue-500"
						onClick={() => navigate(-1)}
						size={20}
					/>
					<Title level={3} className="mb-0">
						Qoidabuzarlik tafsilotlari
					</Title>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* User Information */}
					<Card title="Foydalanuvchi ma'lumotlari" size="small">
						<div className="flex items-center gap-3 mb-4">
							<Avatar
								size={64}
								src={infraction.user?.avatar?.path}
							>
								{infraction.user?.fullName?.charAt(0)}
							</Avatar>
							<div>
								<Title level={4} className="mb-1">
									{infraction.user?.fullName}
								</Title>
								<Text type="secondary">{infraction.user?.phone}</Text>
							</div>
						</div>

						<Descriptions column={1} size="small">
							<Descriptions.Item label="Tashkilot">
								{infraction.user?.organization?.name || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Lavozim">
								{infraction.user?.position?.name || '-'}
							</Descriptions.Item>
						</Descriptions>
					</Card>

					{/* Infraction Information */}
					<Card title="Qoidabuzarlik ma'lumotlari" size="small">
						<Descriptions column={1} size="small">
							<Descriptions.Item label="Nomi">
								{infraction.name || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Tavsif">
								{infraction.description || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Qoidabuzarlik sanasi">
								{dayjs(infraction.infractionDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
							</Descriptions.Item>
							<Descriptions.Item label="Yaratilgan sana">
								{dayjs(infraction.createdAt).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
							</Descriptions.Item>
						</Descriptions>
					</Card>
				</div>

				{/* Infraction Reasons */}
				{infraction.InfractionReason && infraction.InfractionReason.length > 0 && (
					<Card title="Qoidabuzarlik sabablari" className="mt-6" size="small">
						<div className="space-y-3">
							{infraction.InfractionReason.map((reason, index) => (
								<Card key={reason.id} size="small" className="bg-gray-50">
									<div className="flex justify-between items-start">
										<div className="flex-1">
											<Text strong>Sabab #{index + 1}</Text>
											{reason.name && (
												<div className="mt-1">
													<Text>Nomi: {reason.name}</Text>
												</div>
											)}
											{reason.description && (
												<div className="mt-1">
													<Text>Tavsif: {reason.description}</Text>
												</div>
											)}
										</div>
										{reason.file && (
											<Tag color="blue">Fayl mavjud</Tag>
										)}
									</div>
								</Card>
							))}
						</div>
					</Card>
				)}
			</Card>
		</div>
	)
}

export default InfractionDetails