import { Module } from '@nestjs/common';
import { AuthGuard, PrismaModule } from './common';
import { BullModule } from '@nestjs/bullmq';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { AuthModule } from './modules/auth/auth.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { CacheModule } from '@nestjs/cache-manager';
import { APP_GUARD } from '@nestjs/core';

import { EventEmitterModule } from '@nestjs/event-emitter';
import { TaskTypeModule } from './modules/task-type/task-type.module';
import { OrganizationModule } from './modules/organization/organization.module';
import { ReferencesModule } from './modules/references/references.module';
import { TaskStateModule } from './modules/task-state/task-state.module';
import { FaceIdModule } from './modules/face-id/face-id.module';
import { AttendanceModule } from './modules/attendance/attendance.module';
import { UserModule } from './modules/user/user.module';
import { RecipientAnswerTypeModule } from './modules/recipient-answer-type/recipient-answer-type.module';
import { TaskModule } from './modules/task/task.module';
import { UserWorkingScheduleDayModule } from './modules/user-working-schedule-day/user-working-schedule-day.module';
import { UserWorkingScheduleModule } from './modules/user-working-schedule/user-working-schedule.module';
import { GpsModule } from './modules/gps/gps.module';
import { FileModule } from './modules/file/file.module';
import { ScheduleModule } from '@nestjs/schedule';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { RecipientModule } from './modules/recipient/recipient.module';
import * as cl from 'cluster';
import { AppController } from './app.controller';
import { CoordsModule } from './modules/coords/coords.module';
import { StatsModule } from './modules/stats/stats.module';
import { NotificationModule } from './modules/notification/notification.module';
import { DynamicModelModule } from './modules/dynamic-model/dynamic-model.module';
import { VacancyModule } from './modules/vacancy/vacancy.module';
import { VacationModule } from './modules/vacation/vacation.module';
import { BotModule } from './modules/telegram-bot/bot.module';
import { SectionStatsExcelModule } from './modules/dashboard/section-stats/excel/section-stats-excel.module';
import { WorkerModule } from './modules/worker/worker.module';
import { createKeyv } from '@keyv/redis';
import { InfractionModule } from './modules/infraction/infraction.module';

const cluster = cl as unknown as cl.Cluster;
const isSingleProcess = process.env.NODE_ENV === 'development' || cluster.isPrimary;

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    ScheduleModule.forRoot(),
    PrismaModule,
    AuthModule,
    TaskTypeModule,
    TaskStateModule,
    RecipientAnswerTypeModule,
    OrganizationModule,
    TaskModule,
    UserWorkingScheduleDayModule,
    UserWorkingScheduleModule,
    DynamicModelModule,
    SectionStatsExcelModule,
    WorkerModule,
    JwtModule.registerAsync({
      global: true,
      useFactory: (configService: ConfigService) => ({
        global: true,
        secret: configService.get('JWT_SECRET'),
        signOptions: { expiresIn: '60s' },
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: (configService: ConfigService) => {
        const host = configService.get<string | undefined>('REDIS_HOST');
        const port = configService.get<string | undefined>('REDIS_PORT');
        const password = configService.get<string | undefined>('REDIS_PASSWORD_ENV');

        let redisUrl = 'redis://';

        if (host) {
          if (password) {
            redisUrl += `:${password}@${host}`;
          } else {
            redisUrl += host;
          }

          if (port) {
            redisUrl += `:${port}`;
          }
        } else {
          // Default values agar environment variables mavjud bo'lmasa
          redisUrl = 'redis://localhost:6379';
        }

        return {
          stores: [createKeyv(redisUrl)],
        };
      },
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get<string>('REDIS_HOST'),
          port: configService.get<number>('REDIS_PORT'),
          password: configService.get<string>('REDIS_PASSWORD_ENV'),
        },
      }),
      inject: [ConfigService],
    }),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
    }),
    BotModule,
    EventEmitterModule.forRoot(),
    OrganizationModule,
    ReferencesModule,
    FaceIdModule,
    AttendanceModule,
    UserModule,
    GpsModule,
    FileModule,
    DashboardModule,
    RecipientModule,
    CoordsModule,
    StatsModule,
    NotificationModule,
    VacancyModule,
    VacationModule,
    InfractionModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class AppModule {}
